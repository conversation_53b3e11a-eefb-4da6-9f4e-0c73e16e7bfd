-- البيانات الأولية للنظام
USE ngo_management_system;

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, display_name, description, is_system) VALUES
('super_admin', 'مدير النظام الرئيسي', 'صلاحيات كاملة على النظام', TRUE),
('admin', 'مدير النظام', 'صلاحيات إدارية عامة', TRUE),
('org_admin', 'مدير المنظمة', 'مدير المنظمة أو الجمعية', FALSE),
('hr_manager', 'مدير الموارد البشرية', 'إدارة الموظفين والمتطوعين', FALSE),
('project_manager', 'مدير المشاريع', 'إدارة المشاريع والبرامج', FALSE),
('finance_manager', 'مدير مالي', 'إدارة الشؤون المالية والمحاسبة', FALSE),
('warehouse_manager', 'مدير المستودع', 'إدارة المخزون والمستودعات', FALSE),
('employee', 'موظف', 'موظف عادي في المنظمة', FALSE),
('volunteer', 'متطوع', 'متطوع في المنظمة', FALSE),
('viewer', 'مستعرض', 'صلاحيات عرض فقط', FALSE);

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, display_name, module, description) VALUES
-- صلاحيات إدارة النظام
('system.manage', 'إدارة النظام', 'system', 'إدارة إعدادات النظام العامة'),
('users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين'),
('users.create', 'إنشاء مستخدم', 'users', 'إنشاء مستخدمين جدد'),
('users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين'),
('users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين'),
('roles.manage', 'إدارة الأدوار', 'roles', 'إدارة الأدوار والصلاحيات'),

-- صلاحيات إدارة المنظمات
('organizations.view', 'عرض المنظمات', 'organizations', 'عرض قائمة المنظمات'),
('organizations.create', 'إنشاء منظمة', 'organizations', 'إنشاء منظمات جديدة'),
('organizations.edit', 'تعديل المنظمات', 'organizations', 'تعديل بيانات المنظمات'),
('organizations.delete', 'حذف المنظمات', 'organizations', 'حذف المنظمات'),
('organizations.approve', 'اعتماد المنظمات', 'organizations', 'اعتماد وتقييم المنظمات'),

-- صلاحيات إدارة الموظفين
('employees.view', 'عرض الموظفين', 'employees', 'عرض قائمة الموظفين'),
('employees.create', 'إنشاء موظف', 'employees', 'إضافة موظفين جدد'),
('employees.edit', 'تعديل الموظفين', 'employees', 'تعديل بيانات الموظفين'),
('employees.delete', 'حذف الموظفين', 'employees', 'حذف الموظفين'),

-- صلاحيات إدارة المشاريع
('projects.view', 'عرض المشاريع', 'projects', 'عرض قائمة المشاريع'),
('projects.create', 'إنشاء مشروع', 'projects', 'إنشاء مشاريع جديدة'),
('projects.edit', 'تعديل المشاريع', 'projects', 'تعديل بيانات المشاريع'),
('projects.delete', 'حذف المشاريع', 'projects', 'حذف المشاريع'),
('projects.approve', 'اعتماد المشاريع', 'projects', 'اعتماد وتقييم المشاريع'),

-- صلاحيات إدارة المتطوعين
('volunteers.view', 'عرض المتطوعين', 'volunteers', 'عرض قائمة المتطوعين'),
('volunteers.create', 'إنشاء متطوع', 'volunteers', 'إضافة متطوعين جدد'),
('volunteers.edit', 'تعديل المتطوعين', 'volunteers', 'تعديل بيانات المتطوعين'),
('volunteers.delete', 'حذف المتطوعين', 'volunteers', 'حذف المتطوعين'),

-- صلاحيات إدارة التبرعات
('donations.view', 'عرض التبرعات', 'donations', 'عرض قائمة التبرعات'),
('donations.create', 'إنشاء تبرع', 'donations', 'تسجيل تبرعات جديدة'),
('donations.edit', 'تعديل التبرعات', 'donations', 'تعديل بيانات التبرعات'),
('donations.delete', 'حذف التبرعات', 'donations', 'حذف التبرعات'),
('donations.approve', 'اعتماد التبرعات', 'donations', 'اعتماد ومراجعة التبرعات'),

-- صلاحيات إدارة المستودعات
('warehouses.view', 'عرض المستودعات', 'warehouses', 'عرض قائمة المستودعات'),
('warehouses.create', 'إنشاء مستودع', 'warehouses', 'إنشاء مستودعات جديدة'),
('warehouses.edit', 'تعديل المستودعات', 'warehouses', 'تعديل بيانات المستودعات'),
('warehouses.delete', 'حذف المستودعات', 'warehouses', 'حذف المستودعات'),
('inventory.manage', 'إدارة المخزون', 'inventory', 'إدارة المخزون والمواد'),

-- صلاحيات إدارة المالية
('finance.view', 'عرض المالية', 'finance', 'عرض البيانات المالية'),
('finance.create', 'إنشاء قيود مالية', 'finance', 'إنشاء قيود محاسبية'),
('finance.edit', 'تعديل المالية', 'finance', 'تعديل البيانات المالية'),
('finance.approve', 'اعتماد المالية', 'finance', 'اعتماد القيود المالية'),
('reports.financial', 'التقارير المالية', 'reports', 'عرض التقارير المالية'),

-- صلاحيات إدارة الاجتماعات
('meetings.view', 'عرض الاجتماعات', 'meetings', 'عرض قائمة الاجتماعات'),
('meetings.create', 'إنشاء اجتماع', 'meetings', 'إنشاء اجتماعات جديدة'),
('meetings.edit', 'تعديل الاجتماعات', 'meetings', 'تعديل بيانات الاجتماعات'),
('meetings.delete', 'حذف الاجتماعات', 'meetings', 'حذف الاجتماعات'),

-- صلاحيات إدارة المستندات
('documents.view', 'عرض المستندات', 'documents', 'عرض قائمة المستندات'),
('documents.upload', 'رفع المستندات', 'documents', 'رفع مستندات جديدة'),
('documents.edit', 'تعديل المستندات', 'documents', 'تعديل بيانات المستندات'),
('documents.delete', 'حذف المستندات', 'documents', 'حذف المستندات'),

-- صلاحيات التقارير
('reports.view', 'عرض التقارير', 'reports', 'عرض التقارير العامة'),
('reports.export', 'تصدير التقارير', 'reports', 'تصدير التقارير'),
('dashboard.view', 'عرض لوحة التحكم', 'dashboard', 'عرض لوحة التحكم'),

-- صلاحيات سجل التدقيق
('audit.view', 'عرض سجل التدقيق', 'audit', 'عرض سجل التدقيق والمراجعة');

-- ربط الصلاحيات بالأدوار
-- مدير النظام الرئيسي - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير النظام - معظم الصلاحيات عدا إدارة النظام
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name != 'system.manage';

-- مدير المنظمة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE module IN ('organizations', 'employees', 'projects', 'volunteers', 'donations', 'meetings', 'documents', 'reports', 'dashboard');

-- مدير الموارد البشرية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE module IN ('employees', 'volunteers', 'reports', 'dashboard');

-- مدير المشاريع
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE module IN ('projects', 'volunteers', 'reports', 'dashboard');

-- مدير مالي
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE module IN ('finance', 'donations', 'reports', 'dashboard');

-- مدير المستودع
INSERT INTO role_permissions (role_id, permission_id)
SELECT 7, id FROM permissions WHERE module IN ('warehouses', 'inventory', 'reports', 'dashboard');

-- موظف
INSERT INTO role_permissions (role_id, permission_id)
SELECT 8, id FROM permissions WHERE name LIKE '%.view' OR module = 'dashboard';

-- متطوع
INSERT INTO role_permissions (role_id, permission_id)
SELECT 9, id FROM permissions WHERE name IN ('projects.view', 'volunteers.view', 'dashboard.view');

-- مستعرض
INSERT INTO role_permissions (role_id, permission_id)
SELECT 10, id FROM permissions WHERE name LIKE '%.view';

-- إنشاء المستخدم الرئيسي
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', '**********', 'active', TRUE);

-- ربط المستخدم الرئيسي بدور مدير النظام الرئيسي
INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (1, 1, 1);

-- إنشاء الحسابات المالية الأساسية
INSERT INTO accounts (organization_id, account_code, account_name, account_type, level) VALUES
(NULL, '1000', 'الأصول', 'asset', 1),
(NULL, '1100', 'الأصول المتداولة', 'asset', 2),
(NULL, '1110', 'النقدية', 'asset', 3),
(NULL, '1120', 'البنوك', 'asset', 3),
(NULL, '1130', 'المدينون', 'asset', 3),
(NULL, '1140', 'المخزون', 'asset', 3),
(NULL, '1200', 'الأصول الثابتة', 'asset', 2),
(NULL, '1210', 'الأراضي والمباني', 'asset', 3),
(NULL, '1220', 'المعدات والأثاث', 'asset', 3),
(NULL, '2000', 'الخصوم', 'liability', 1),
(NULL, '2100', 'الخصوم المتداولة', 'liability', 2),
(NULL, '2110', 'الدائنون', 'liability', 3),
(NULL, '2120', 'المصروفات المستحقة', 'liability', 3),
(NULL, '3000', 'حقوق الملكية', 'equity', 1),
(NULL, '3100', 'رأس المال', 'equity', 2),
(NULL, '3200', 'الأرباح المحتجزة', 'equity', 2),
(NULL, '4000', 'الإيرادات', 'revenue', 1),
(NULL, '4100', 'التبرعات', 'revenue', 2),
(NULL, '4200', 'المنح', 'revenue', 2),
(NULL, '4300', 'إيرادات أخرى', 'revenue', 2),
(NULL, '5000', 'المصروفات', 'expense', 1),
(NULL, '5100', 'مصروفات إدارية', 'expense', 2),
(NULL, '5200', 'مصروفات المشاريع', 'expense', 2),
(NULL, '5300', 'مصروفات أخرى', 'expense', 2);
