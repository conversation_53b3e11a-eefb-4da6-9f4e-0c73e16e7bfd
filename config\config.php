<?php
/**
 * Application Configuration
 * نظام إدارة مؤسسات القطاع غير الربحي
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Define constants
define('APP_NAME', 'نظام إدارة مؤسسات القطاع غير الربحي');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/CMS2/');
define('ROOT_PATH', dirname(__DIR__) . '/');
define('UPLOAD_PATH', ROOT_PATH . 'uploads/');
define('LOGS_PATH', ROOT_PATH . 'logs/');

// Security settings
define('JWT_SECRET', 'your-secret-key-here-change-in-production');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif']);

// Pagination settings
define('RECORDS_PER_PAGE', 20);

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Language settings
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Timezone
date_default_timezone_set('Asia/Riyadh');

// Error reporting
if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Autoloader
spl_autoload_register(function ($class) {
    $directories = [
        ROOT_PATH . 'models/',
        ROOT_PATH . 'controllers/',
        ROOT_PATH . 'core/',
        ROOT_PATH . 'helpers/',
        ROOT_PATH . 'middleware/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Include required files
require_once ROOT_PATH . 'config/database.php';
require_once ROOT_PATH . 'core/Router.php';
require_once ROOT_PATH . 'core/Controller.php';
require_once ROOT_PATH . 'core/Model.php';
require_once ROOT_PATH . 'helpers/Security.php';
require_once ROOT_PATH . 'helpers/Validator.php';
require_once ROOT_PATH . 'helpers/FileUpload.php';

// Create necessary directories
$directories = [UPLOAD_PATH, LOGS_PATH];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
