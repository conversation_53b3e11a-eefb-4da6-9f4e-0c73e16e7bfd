<?php
/**
 * Router Class
 * نظام التوجيه للتطبيق
 */

class Router {
    private $routes = [];
    private $middlewares = [];
    
    public function __construct() {
        $this->loadRoutes();
    }
    
    public function get($path, $callback, $middleware = []) {
        $this->addRoute('GET', $path, $callback, $middleware);
    }
    
    public function post($path, $callback, $middleware = []) {
        $this->addRoute('POST', $path, $callback, $middleware);
    }
    
    public function put($path, $callback, $middleware = []) {
        $this->addRoute('PUT', $path, $callback, $middleware);
    }
    
    public function delete($path, $callback, $middleware = []) {
        $this->addRoute('DELETE', $path, $callback, $middleware);
    }
    
    private function addRoute($method, $path, $callback, $middleware = []) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'callback' => $callback,
            'middleware' => $middleware
        ];
    }
    
    public function dispatch() {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove base path if exists
        $basePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', ROOT_PATH);
        $basePath = rtrim($basePath, '/');
        if ($basePath && strpos($requestUri, $basePath) === 0) {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        $requestUri = rtrim($requestUri, '/') ?: '/';
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchPath($route['path'], $requestUri)) {
                // Execute middleware
                foreach ($route['middleware'] as $middleware) {
                    $middlewareClass = new $middleware();
                    if (!$middlewareClass->handle()) {
                        return;
                    }
                }
                
                // Execute callback
                if (is_callable($route['callback'])) {
                    call_user_func($route['callback']);
                } elseif (is_string($route['callback'])) {
                    $this->executeController($route['callback'], $requestUri, $route['path']);
                }
                return;
            }
        }
        
        // 404 Not Found
        $this->notFound();
    }
    
    private function matchPath($routePath, $requestUri) {
        // Convert route path to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestUri);
    }
    
    private function executeController($callback, $requestUri, $routePath) {
        list($controller, $method) = explode('@', $callback);
        
        if (!class_exists($controller)) {
            throw new Exception("Controller {$controller} not found");
        }
        
        $controllerInstance = new $controller();
        
        if (!method_exists($controllerInstance, $method)) {
            throw new Exception("Method {$method} not found in {$controller}");
        }
        
        // Extract parameters from URL
        $params = $this->extractParams($routePath, $requestUri);
        
        call_user_func_array([$controllerInstance, $method], $params);
    }
    
    private function extractParams($routePath, $requestUri) {
        $routeParts = explode('/', trim($routePath, '/'));
        $uriParts = explode('/', trim($requestUri, '/'));
        
        $params = [];
        for ($i = 0; $i < count($routeParts); $i++) {
            if (preg_match('/\{([^}]+)\}/', $routeParts[$i])) {
                $params[] = $uriParts[$i] ?? null;
            }
        }
        
        return $params;
    }
    
    private function loadRoutes() {
        // Default routes
        $this->get('/', 'HomeController@index');
        $this->get('/login', 'AuthController@showLogin');
        $this->post('/login', 'AuthController@login');
        $this->get('/logout', 'AuthController@logout', ['AuthMiddleware']);
        $this->get('/register', 'AuthController@showRegister');
        $this->post('/register', 'AuthController@register');
        
        // Dashboard
        $this->get('/dashboard', 'DashboardController@index', ['AuthMiddleware']);
        
        // Users management
        $this->get('/users', 'UserController@index', ['AuthMiddleware', 'PermissionMiddleware:users.view']);
        $this->get('/users/create', 'UserController@create', ['AuthMiddleware', 'PermissionMiddleware:users.create']);
        $this->post('/users', 'UserController@store', ['AuthMiddleware', 'PermissionMiddleware:users.create']);
        $this->get('/users/{id}', 'UserController@show', ['AuthMiddleware', 'PermissionMiddleware:users.view']);
        $this->get('/users/{id}/edit', 'UserController@edit', ['AuthMiddleware', 'PermissionMiddleware:users.edit']);
        $this->put('/users/{id}', 'UserController@update', ['AuthMiddleware', 'PermissionMiddleware:users.edit']);
        $this->delete('/users/{id}', 'UserController@delete', ['AuthMiddleware', 'PermissionMiddleware:users.delete']);
        
        // Organizations management
        $this->get('/organizations', 'OrganizationController@index', ['AuthMiddleware', 'PermissionMiddleware:organizations.view']);
        $this->get('/organizations/create', 'OrganizationController@create', ['AuthMiddleware', 'PermissionMiddleware:organizations.create']);
        $this->post('/organizations', 'OrganizationController@store', ['AuthMiddleware', 'PermissionMiddleware:organizations.create']);
        $this->get('/organizations/{id}', 'OrganizationController@show', ['AuthMiddleware', 'PermissionMiddleware:organizations.view']);
        $this->get('/organizations/{id}/edit', 'OrganizationController@edit', ['AuthMiddleware', 'PermissionMiddleware:organizations.edit']);
        $this->put('/organizations/{id}', 'OrganizationController@update', ['AuthMiddleware', 'PermissionMiddleware:organizations.edit']);
        $this->delete('/organizations/{id}', 'OrganizationController@delete', ['AuthMiddleware', 'PermissionMiddleware:organizations.delete']);
        
        // Employees management
        $this->get('/employees', 'EmployeeController@index', ['AuthMiddleware', 'PermissionMiddleware:employees.view']);
        $this->get('/employees/create', 'EmployeeController@create', ['AuthMiddleware', 'PermissionMiddleware:employees.create']);
        $this->post('/employees', 'EmployeeController@store', ['AuthMiddleware', 'PermissionMiddleware:employees.create']);
        $this->get('/employees/{id}', 'EmployeeController@show', ['AuthMiddleware', 'PermissionMiddleware:employees.view']);
        $this->get('/employees/{id}/edit', 'EmployeeController@edit', ['AuthMiddleware', 'PermissionMiddleware:employees.edit']);
        $this->put('/employees/{id}', 'EmployeeController@update', ['AuthMiddleware', 'PermissionMiddleware:employees.edit']);
        $this->delete('/employees/{id}', 'EmployeeController@delete', ['AuthMiddleware', 'PermissionMiddleware:employees.delete']);
        
        // Projects management
        $this->get('/projects', 'ProjectController@index', ['AuthMiddleware', 'PermissionMiddleware:projects.view']);
        $this->get('/projects/create', 'ProjectController@create', ['AuthMiddleware', 'PermissionMiddleware:projects.create']);
        $this->post('/projects', 'ProjectController@store', ['AuthMiddleware', 'PermissionMiddleware:projects.create']);
        $this->get('/projects/{id}', 'ProjectController@show', ['AuthMiddleware', 'PermissionMiddleware:projects.view']);
        $this->get('/projects/{id}/edit', 'ProjectController@edit', ['AuthMiddleware', 'PermissionMiddleware:projects.edit']);
        $this->put('/projects/{id}', 'ProjectController@update', ['AuthMiddleware', 'PermissionMiddleware:projects.edit']);
        $this->delete('/projects/{id}', 'ProjectController@delete', ['AuthMiddleware', 'PermissionMiddleware:projects.delete']);
        
        // Volunteers management
        $this->get('/volunteers', 'VolunteerController@index', ['AuthMiddleware', 'PermissionMiddleware:volunteers.view']);
        $this->get('/volunteers/create', 'VolunteerController@create', ['AuthMiddleware', 'PermissionMiddleware:volunteers.create']);
        $this->post('/volunteers', 'VolunteerController@store', ['AuthMiddleware', 'PermissionMiddleware:volunteers.create']);
        $this->get('/volunteers/{id}', 'VolunteerController@show', ['AuthMiddleware', 'PermissionMiddleware:volunteers.view']);
        $this->get('/volunteers/{id}/edit', 'VolunteerController@edit', ['AuthMiddleware', 'PermissionMiddleware:volunteers.edit']);
        $this->put('/volunteers/{id}', 'VolunteerController@update', ['AuthMiddleware', 'PermissionMiddleware:volunteers.edit']);
        $this->delete('/volunteers/{id}', 'VolunteerController@delete', ['AuthMiddleware', 'PermissionMiddleware:volunteers.delete']);
        
        // Donations management
        $this->get('/donations', 'DonationController@index', ['AuthMiddleware', 'PermissionMiddleware:donations.view']);
        $this->get('/donations/create', 'DonationController@create', ['AuthMiddleware', 'PermissionMiddleware:donations.create']);
        $this->post('/donations', 'DonationController@store', ['AuthMiddleware', 'PermissionMiddleware:donations.create']);
        $this->get('/donations/{id}', 'DonationController@show', ['AuthMiddleware', 'PermissionMiddleware:donations.view']);
        $this->get('/donations/{id}/edit', 'DonationController@edit', ['AuthMiddleware', 'PermissionMiddleware:donations.edit']);
        $this->put('/donations/{id}', 'DonationController@update', ['AuthMiddleware', 'PermissionMiddleware:donations.edit']);
        $this->delete('/donations/{id}', 'DonationController@delete', ['AuthMiddleware', 'PermissionMiddleware:donations.delete']);
        
        // API routes
        $this->get('/api/users', 'Api\\UserController@index', ['AuthMiddleware', 'ApiMiddleware']);
        $this->get('/api/organizations', 'Api\\OrganizationController@index', ['AuthMiddleware', 'ApiMiddleware']);
    }
    
    private function notFound() {
        http_response_code(404);
        include ROOT_PATH . 'views/errors/404.php';
    }
}
